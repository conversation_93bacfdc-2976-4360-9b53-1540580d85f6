// 用于生成文本摘要的组合式函数
import { ref, computed } from "vue";
import { useMessage } from "naive-ui";
import { useModelStore } from "@/store/useModelStore.ts";
import { useChatStore } from "@/store/useChatStore.ts";
import { diContainer } from "@/usecases/container/DIContainer";

/**
 * useSummary 组合式函数 - 纯 UI 逻辑
 * 提供文本摘要生成功能及相关状态，业务逻辑委托给 SummaryUseCase。
 */
export function useSummary() {
    // 加载状态
    const isLoading = ref(false);
    // 错误信息
    const error = ref<string | null>(null);
    // 最后一次生成的摘要
    const lastSummary = ref<string | null>(null);

    // 获取 naive-ui 的消息实例
    const message = useMessage();
    // 获取模型配置 store
    const modelStore = useModelStore();
    // 获取聊天配置 store
    const chatStore = useChatStore();

    // 是否可以生成摘要（模型配置是否有效）
    const canGenerate = computed(() => modelStore.isModelConfigured().isValid);

    /**
     * 生成摘要
     * @param originText 原始文本
     * @returns 摘要文本
     */
    const generateSummary = async (originText: string): Promise<string> => {
        error.value = null;
        lastSummary.value = null;

        if (!canGenerate.value) {
            const errText = "模型配置不完整，请检查模型、API密钥和基础URL设置";
            error.value = errText;
            message.error(errText);
            return originText;
        }

        isLoading.value = true;
        try {
            let openai;
            let model;

            // 如果启用了单独的总结模型，使用指定的模型和供应商
            if (chatStore.enableSeparateSummaryModel &&
                chatStore.summaryModel &&
                chatStore.summaryProvider) {

                openai = await modelStore.createOpenAIInstance(chatStore.summaryProvider);
                model = chatStore.summaryModel;

                if (!openai) {
                    console.warn("创建总结模型实例失败，回退到当前模型");
                    // 回退到当前模型
                    openai = await modelStore.createOpenAIInstance();
                    model = modelStore.model;
                }
            } else {
                // 使用当前模型进行总结
                openai = await modelStore.createOpenAIInstance();
                model = modelStore.model;
            }

            if (!openai) {
                const errText = "创建 OpenAI 实例失败，请检查配置。";
                error.value = errText;
                message.error(errText);
                return originText;
            }

            // 使用 SummaryUseCase 生成摘要
            const summaryUseCase = diContainer.summaryUseCase;
            const summaryResult = await summaryUseCase.execute({
                originText,
                openai,
                model
            });

            if (summaryResult.success) {
                lastSummary.value = summaryResult.summary;
                return summaryResult.summary;
            } else {
                throw new Error(summaryResult.error || "摘要生成失败");
            }
        } catch (e: any) {
            const errText = e.message || "总结生成失败，请稍后再试";
            error.value = errText;
            message.error(errText);
            return originText;
        } finally {
            isLoading.value = false;
        }
    };

    // 清除错误
    const clearError = () => {
        error.value = null;
    };

    // 重置所有状态
    const reset = () => {
        isLoading.value = false;
        error.value = null;
        lastSummary.value = null;
    };

    return {
        isLoading,
        error,
        lastSummary,
        canGenerate,
        generateSummary,
        clearError,
        reset,
    };
}
